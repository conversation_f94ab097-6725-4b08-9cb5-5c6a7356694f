<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Traits\CsvDataTrimmer;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class MobileModelController extends Controller
{
    use CsvDataTrimmer;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MobileModel::with('brand')
            ->withCount('parts');

        // Apply search
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('model_number', 'LIKE', "%{$search}%")
                  ->orWhereHas('brand', function ($brandQuery) use ($search) {
                      $brandQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply brand filter
        if ($brandId = $request->get('brand_id')) {
            $query->where('brand_id', $brandId);
        }

        // Apply status filter
        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply release year filter
        if ($releaseYear = $request->get('release_year')) {
            $query->where('release_year', $releaseYear);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'model_number', 'release_year', 'created_at', 'updated_at'])) {
            if ($sortBy === 'brand_name') {
                $query->join('brands', 'models.brand_id', '=', 'brands.id')
                      ->orderBy('brands.name', $sortOrder)
                      ->select('models.*');
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        $models = $query->paginate(15)->withQueryString();

        // Get filter options
        $brands = Brand::active()->orderBy('name')->get(['id', 'name']);
        $releaseYears = MobileModel::select('release_year')
            ->whereNotNull('release_year')
            ->distinct()
            ->orderBy('release_year', 'desc')
            ->pluck('release_year');

        return Inertia::render('admin/Models/Index', [
            'models' => $models,
            'filters' => [
                'brands' => $brands,
                'release_years' => $releaseYears,
            ],
            'queryParams' => $request->only(['search', 'brand_id', 'status', 'release_year', 'sort_by', 'sort_order', 'view']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $brands = Brand::active()->orderBy('name')->get();

        return Inertia::render('admin/Models/Create', [
            'brands' => $brands,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Trim input data before validation
        $trimmedInput = $this->trimCsvFields($request->all(), ['name', 'model_number', 'image_url']);
        $request->merge($trimmedInput);

        $validated = $request->validate([
            'brand_id' => 'required|exists:brands,id',
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('models')->where(function ($query) use ($request) {
                    return $query->where('brand_id', $request->brand_id);
                })
            ],
            'model_number' => 'nullable|string|max:255',
            'release_year' => 'nullable|integer|min:2000|max:' . (date('Y') + 1),
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'image_url' => 'nullable|url|max:2048',
            'is_active' => 'boolean',
        ]);

        try {
            MobileModel::create($validated);

            return redirect()->route('admin.models.index')
                ->with('success', 'Model created successfully.');

        } catch (\Illuminate\Database\UniqueConstraintViolationException $e) {
            \Log::warning('Model creation failed due to unique constraint violation', [
                'brand_id' => $request->brand_id,
                'name' => $request->name,
                'error' => $e->getMessage()
            ]);

            return back()
                ->withInput()
                ->withErrors(['name' => 'A model with this name already exists for the selected brand.']);

        } catch (\Exception $e) {
            \Log::error('Failed to create model', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withInput()
                ->with('error', 'Failed to create model. Please try again or contact support.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MobileModel $model)
    {
        $model->load('brand', 'parts.category');

        return Inertia::render('admin/Models/Show', [
            'model' => $model,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MobileModel $model)
    {
        $brands = Brand::active()->orderBy('name')->get();

        return Inertia::render('admin/Models/Edit', [
            'model' => $model,
            'brands' => $brands,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MobileModel $model)
    {
        // Trim input data before validation
        $trimmedInput = $this->trimCsvFields($request->all(), ['name', 'model_number', 'image_url']);
        $request->merge($trimmedInput);

        $validated = $request->validate([
            'brand_id' => 'required|exists:brands,id',
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('models')->where(function ($query) use ($request) {
                    return $query->where('brand_id', $request->brand_id);
                })->ignore($model->id)
            ],
            'model_number' => 'nullable|string|max:255',
            'release_year' => 'nullable|integer|min:2000|max:' . (date('Y') + 1),
            'specifications' => 'nullable|array',
            'images' => 'nullable|array',
            'image_url' => 'nullable|url|max:2048',
            'is_active' => 'boolean',
        ]);

        try {
            $model->update($validated);

            return redirect()->route('admin.models.index')
                ->with('success', 'Model updated successfully.');

        } catch (\Illuminate\Database\UniqueConstraintViolationException $e) {
            \Log::warning('Model update failed due to unique constraint violation', [
                'model_id' => $model->id,
                'brand_id' => $request->brand_id,
                'name' => $request->name,
                'error' => $e->getMessage()
            ]);

            return back()
                ->withInput()
                ->withErrors(['name' => 'A model with this name already exists for the selected brand.']);

        } catch (\Exception $e) {
            \Log::error('Failed to update model', [
                'model_id' => $model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withInput()
                ->with('error', 'Failed to update model. Please try again or contact support.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MobileModel $model)
    {
        // Check if model has parts
        if ($model->parts()->count() > 0) {
            return back()->with('error', 'Cannot delete model with associated parts.');
        }

        $model->delete();

        return redirect()->route('admin.models.index')
            ->with('success', 'Model deleted successfully.');
    }

    /**
     * Export models to CSV.
     */
    public function export(Request $request)
    {
        $query = MobileModel::with('brand')->withCount('parts');

        // Apply the same filters as the index method
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('model_number', 'LIKE', "%{$search}%")
                  ->orWhereHas('brand', function ($brandQuery) use ($search) {
                      $brandQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        if ($brandId = $request->get('brand_id')) {
            $query->where('brand_id', $brandId);
        }

        if ($status = $request->get('status')) {
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($releaseYear = $request->get('release_year')) {
            $query->where('release_year', $releaseYear);
        }

        // Handle selected IDs for bulk export
        if ($request->has('ids')) {
            $ids = $request->get('ids');
            if (is_array($ids) && !empty($ids)) {
                $query->whereIn('id', $ids);
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'model_number', 'release_year', 'created_at', 'updated_at'])) {
            if ($sortBy === 'brand_name') {
                $query->join('brands', 'models.brand_id', '=', 'brands.id')
                      ->orderBy('brands.name', $sortOrder)
                      ->select('models.*');
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }
        } else {
            $query->orderBy('name', 'asc');
        }

        $models = $query->get();

        // Prepare CSV data
        $csvData = [];
        $csvData[] = ['Brand', 'Model Name', 'Model Number', 'Release Year', 'Specifications', 'Image URL', 'Status', 'Parts Count', 'Created Date'];

        foreach ($models as $model) {
            // Format specifications as a simple string to avoid CSV parsing issues
            $specifications = '';
            if ($model->specifications && is_array($model->specifications)) {
                $specParts = [];
                foreach ($model->specifications as $key => $value) {
                    $specParts[] = $key . ': ' . $value;
                }
                $specifications = implode('; ', $specParts);
            }

            $csvData[] = [
                $model->brand->name ?? '',
                $model->name ?? '',
                $model->model_number ?? '',
                $model->release_year ?? '',
                $specifications,
                $model->image_url ?? '',
                $model->is_active ? 'Active' : 'Inactive',
                $model->parts_count ?? 0,
                $model->created_at ? $model->created_at->format('Y-m-d H:i:s') : '',
            ];
        }

        $filename = 'models_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * Download CSV template for models import.
     */
    public function downloadTemplate()
    {
        $csvData = [];
        $csvData[] = ['Brand Name', 'Model Name', 'Model Number', 'Release Year', 'Specifications', 'Image URL', 'Status'];
        $csvData[] = ['# IMPORT TEMPLATE: Use this template to import mobile models'];
        $csvData[] = ['# Brand Name: Must match an existing brand name exactly'];
        $csvData[] = ['# Model Name: Required - the name of the mobile model'];
        $csvData[] = ['# Model Number: Optional - manufacturer model number'];
        $csvData[] = ['# Release Year: Optional - year the model was released (2000-' . (date('Y') + 1) . ')'];
        $csvData[] = ['# Specifications: Optional - can be empty, JSON format, or key:value format'];
        $csvData[] = ['# Image URL: Optional - URL to the primary image of the model'];
        $csvData[] = ['# Status: Active or Inactive (defaults to Active if empty)'];
        $csvData[] = ['Apple', 'iPhone 15 Pro', 'A3101', '2023', 'display: 6.1 inch; storage: 128GB; camera: 48MP', 'https://example.com/iphone15pro.jpg', 'Active'];
        $csvData[] = ['Samsung', 'Galaxy S24 Ultra', 'SM-S928B', '2024', '{"display":"6.8 inch","storage":"256GB","camera":"200MP"}', 'https://example.com/galaxys24ultra.jpg', 'Active'];
        $csvData[] = ['Google', 'Pixel 8', 'GA04834', '2023', '', '', 'Active'];

        $filename = 'models_import_template.csv';

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ',', '"', '\\');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
}
