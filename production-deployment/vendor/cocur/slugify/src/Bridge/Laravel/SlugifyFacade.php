<?php

/**
 * This file is part of cocur/slugify.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cocur\Slugify\Bridge\Laravel;

use Illuminate\Support\Facades\Facade;

/**
 * SlugifyFacade
 *
 * @package    cocur/slugify
 * @subpackage bridge
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright  2012-2014 F<PERSON><PERSON>
 * @license    http://www.opensource.org/licenses/MIT The MIT License
 */
class SlugifyFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    protected static function getFacadeAccessor(): string
    {
        return 'slugify';
    }
}
