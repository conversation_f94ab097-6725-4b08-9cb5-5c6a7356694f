<?php return array (
  '1000CAT' => 
  array (
    'symbol' => '1000CAT',
    'minorUnit' => 8,
  ),
  '1000CHEEMS' => 
  array (
    'symbol' => '1000CHEEMS',
    'minorUnit' => 8,
  ),
  '1000SATS' => 
  array (
    'symbol' => '1000SATS',
    'minorUnit' => 8,
  ),
  '1INCH' => 
  array (
    'symbol' => '1INCH',
    'minorUnit' => 8,
  ),
  '1MBABYDOGE' => 
  array (
    'symbol' => '1MBABYDOGE',
    'minorUnit' => 8,
  ),
  'A' => 
  array (
    'symbol' => 'A',
    'minorUnit' => 8,
  ),
  'AAVE' => 
  array (
    'symbol' => 'AAVE',
    'minorUnit' => 8,
  ),
  'ACA' => 
  array (
    'symbol' => 'ACA',
    'minorUnit' => 8,
  ),
  'ACE' => 
  array (
    'symbol' => 'ACE',
    'minorUnit' => 8,
  ),
  'ACH' => 
  array (
    'symbol' => 'ACH',
    'minorUnit' => 8,
  ),
  'ACM' => 
  array (
    'symbol' => 'ACM',
    'minorUnit' => 8,
  ),
  'ACT' => 
  array (
    'symbol' => 'ACT',
    'minorUnit' => 8,
  ),
  'ACX' => 
  array (
    'symbol' => 'ACX',
    'minorUnit' => 8,
  ),
  'ADA' => 
  array (
    'symbol' => 'ADA',
    'minorUnit' => 8,
  ),
  'ADX' => 
  array (
    'symbol' => 'ADX',
    'minorUnit' => 8,
  ),
  'AEUR' => 
  array (
    'symbol' => 'AEUR',
    'minorUnit' => 8,
  ),
  'AEVO' => 
  array (
    'symbol' => 'AEVO',
    'minorUnit' => 8,
  ),
  'AGLD' => 
  array (
    'symbol' => 'AGLD',
    'minorUnit' => 8,
  ),
  'AI' => 
  array (
    'symbol' => 'AI',
    'minorUnit' => 8,
  ),
  'AIXBT' => 
  array (
    'symbol' => 'AIXBT',
    'minorUnit' => 8,
  ),
  'ALCX' => 
  array (
    'symbol' => 'ALCX',
    'minorUnit' => 8,
  ),
  'ALGO' => 
  array (
    'symbol' => 'ALGO',
    'minorUnit' => 8,
  ),
  'ALICE' => 
  array (
    'symbol' => 'ALICE',
    'minorUnit' => 8,
  ),
  'ALPHA' => 
  array (
    'symbol' => 'ALPHA',
    'minorUnit' => 8,
  ),
  'ALPINE' => 
  array (
    'symbol' => 'ALPINE',
    'minorUnit' => 8,
  ),
  'ALT' => 
  array (
    'symbol' => 'ALT',
    'minorUnit' => 8,
  ),
  'AMP' => 
  array (
    'symbol' => 'AMP',
    'minorUnit' => 8,
  ),
  'ANIME' => 
  array (
    'symbol' => 'ANIME',
    'minorUnit' => 8,
  ),
  'ANKR' => 
  array (
    'symbol' => 'ANKR',
    'minorUnit' => 8,
  ),
  'APE' => 
  array (
    'symbol' => 'APE',
    'minorUnit' => 8,
  ),
  'API3' => 
  array (
    'symbol' => 'API3',
    'minorUnit' => 8,
  ),
  'APT' => 
  array (
    'symbol' => 'APT',
    'minorUnit' => 8,
  ),
  'AR' => 
  array (
    'symbol' => 'AR',
    'minorUnit' => 8,
  ),
  'ARB' => 
  array (
    'symbol' => 'ARB',
    'minorUnit' => 8,
  ),
  'ARDR' => 
  array (
    'symbol' => 'ARDR',
    'minorUnit' => 8,
  ),
  'ARK' => 
  array (
    'symbol' => 'ARK',
    'minorUnit' => 8,
  ),
  'ARKM' => 
  array (
    'symbol' => 'ARKM',
    'minorUnit' => 8,
  ),
  'ARPA' => 
  array (
    'symbol' => 'ARPA',
    'minorUnit' => 8,
  ),
  'ARS' => 
  array (
    'symbol' => 'ARS',
    'minorUnit' => 8,
  ),
  'ASR' => 
  array (
    'symbol' => 'ASR',
    'minorUnit' => 8,
  ),
  'ASTR' => 
  array (
    'symbol' => 'ASTR',
    'minorUnit' => 8,
  ),
  'ATA' => 
  array (
    'symbol' => 'ATA',
    'minorUnit' => 8,
  ),
  'ATM' => 
  array (
    'symbol' => 'ATM',
    'minorUnit' => 8,
  ),
  'ATOM' => 
  array (
    'symbol' => 'ATOM',
    'minorUnit' => 8,
  ),
  'AUCTION' => 
  array (
    'symbol' => 'AUCTION',
    'minorUnit' => 8,
  ),
  'AUDIO' => 
  array (
    'symbol' => 'AUDIO',
    'minorUnit' => 8,
  ),
  'AVA' => 
  array (
    'symbol' => 'AVA',
    'minorUnit' => 8,
  ),
  'AVAX' => 
  array (
    'symbol' => 'AVAX',
    'minorUnit' => 8,
  ),
  'AWE' => 
  array (
    'symbol' => 'AWE',
    'minorUnit' => 8,
  ),
  'AXL' => 
  array (
    'symbol' => 'AXL',
    'minorUnit' => 8,
  ),
  'AXS' => 
  array (
    'symbol' => 'AXS',
    'minorUnit' => 8,
  ),
  'BABY' => 
  array (
    'symbol' => 'BABY',
    'minorUnit' => 8,
  ),
  'BAKE' => 
  array (
    'symbol' => 'BAKE',
    'minorUnit' => 8,
  ),
  'BANANA' => 
  array (
    'symbol' => 'BANANA',
    'minorUnit' => 8,
  ),
  'BANANAS31' => 
  array (
    'symbol' => 'BANANAS31',
    'minorUnit' => 8,
  ),
  'BAND' => 
  array (
    'symbol' => 'BAND',
    'minorUnit' => 8,
  ),
  'BAR' => 
  array (
    'symbol' => 'BAR',
    'minorUnit' => 8,
  ),
  'BAT' => 
  array (
    'symbol' => 'BAT',
    'minorUnit' => 8,
  ),
  'BB' => 
  array (
    'symbol' => 'BB',
    'minorUnit' => 8,
  ),
  'BCH' => 
  array (
    'symbol' => 'BCH',
    'minorUnit' => 8,
  ),
  'BEAMX' => 
  array (
    'symbol' => 'BEAMX',
    'minorUnit' => 8,
  ),
  'BEL' => 
  array (
    'symbol' => 'BEL',
    'minorUnit' => 8,
  ),
  'BERA' => 
  array (
    'symbol' => 'BERA',
    'minorUnit' => 8,
  ),
  'BICO' => 
  array (
    'symbol' => 'BICO',
    'minorUnit' => 8,
  ),
  'BIFI' => 
  array (
    'symbol' => 'BIFI',
    'minorUnit' => 8,
  ),
  'BIGTIME' => 
  array (
    'symbol' => 'BIGTIME',
    'minorUnit' => 8,
  ),
  'BIO' => 
  array (
    'symbol' => 'BIO',
    'minorUnit' => 8,
  ),
  'BLUR' => 
  array (
    'symbol' => 'BLUR',
    'minorUnit' => 8,
  ),
  'BMT' => 
  array (
    'symbol' => 'BMT',
    'minorUnit' => 8,
  ),
  'BNB' => 
  array (
    'symbol' => 'BNB',
    'minorUnit' => 8,
  ),
  'BNSOL' => 
  array (
    'symbol' => 'BNSOL',
    'minorUnit' => 8,
  ),
  'BNT' => 
  array (
    'symbol' => 'BNT',
    'minorUnit' => 8,
  ),
  'BOME' => 
  array (
    'symbol' => 'BOME',
    'minorUnit' => 8,
  ),
  'BONK' => 
  array (
    'symbol' => 'BONK',
    'minorUnit' => 8,
  ),
  'BROCCOLI714' => 
  array (
    'symbol' => 'BROCCOLI714',
    'minorUnit' => 8,
  ),
  'BSW' => 
  array (
    'symbol' => 'BSW',
    'minorUnit' => 8,
  ),
  'BTC' => 
  array (
    'symbol' => 'BTC',
    'minorUnit' => 8,
  ),
  'BTTC' => 
  array (
    'symbol' => 'BTTC',
    'minorUnit' => 8,
  ),
  'C98' => 
  array (
    'symbol' => 'C98',
    'minorUnit' => 8,
  ),
  'CAKE' => 
  array (
    'symbol' => 'CAKE',
    'minorUnit' => 8,
  ),
  'CATI' => 
  array (
    'symbol' => 'CATI',
    'minorUnit' => 8,
  ),
  'CELO' => 
  array (
    'symbol' => 'CELO',
    'minorUnit' => 8,
  ),
  'CELR' => 
  array (
    'symbol' => 'CELR',
    'minorUnit' => 8,
  ),
  'CETUS' => 
  array (
    'symbol' => 'CETUS',
    'minorUnit' => 8,
  ),
  'CFX' => 
  array (
    'symbol' => 'CFX',
    'minorUnit' => 8,
  ),
  'CGPT' => 
  array (
    'symbol' => 'CGPT',
    'minorUnit' => 8,
  ),
  'CHESS' => 
  array (
    'symbol' => 'CHESS',
    'minorUnit' => 8,
  ),
  'CHR' => 
  array (
    'symbol' => 'CHR',
    'minorUnit' => 8,
  ),
  'CHZ' => 
  array (
    'symbol' => 'CHZ',
    'minorUnit' => 8,
  ),
  'CITY' => 
  array (
    'symbol' => 'CITY',
    'minorUnit' => 8,
  ),
  'CKB' => 
  array (
    'symbol' => 'CKB',
    'minorUnit' => 8,
  ),
  'COMP' => 
  array (
    'symbol' => 'COMP',
    'minorUnit' => 8,
  ),
  'COOKIE' => 
  array (
    'symbol' => 'COOKIE',
    'minorUnit' => 8,
  ),
  'COP' => 
  array (
    'symbol' => 'COP',
    'minorUnit' => 8,
  ),
  'COS' => 
  array (
    'symbol' => 'COS',
    'minorUnit' => 8,
  ),
  'COTI' => 
  array (
    'symbol' => 'COTI',
    'minorUnit' => 8,
  ),
  'COW' => 
  array (
    'symbol' => 'COW',
    'minorUnit' => 8,
  ),
  'CRV' => 
  array (
    'symbol' => 'CRV',
    'minorUnit' => 8,
  ),
  'CTK' => 
  array (
    'symbol' => 'CTK',
    'minorUnit' => 8,
  ),
  'CTSI' => 
  array (
    'symbol' => 'CTSI',
    'minorUnit' => 8,
  ),
  'CVC' => 
  array (
    'symbol' => 'CVC',
    'minorUnit' => 8,
  ),
  'CVX' => 
  array (
    'symbol' => 'CVX',
    'minorUnit' => 8,
  ),
  'CYBER' => 
  array (
    'symbol' => 'CYBER',
    'minorUnit' => 8,
  ),
  'CZK' => 
  array (
    'symbol' => 'CZK',
    'minorUnit' => 8,
  ),
  'D' => 
  array (
    'symbol' => 'D',
    'minorUnit' => 8,
  ),
  'DAI' => 
  array (
    'symbol' => 'DAI',
    'minorUnit' => 8,
  ),
  'DASH' => 
  array (
    'symbol' => 'DASH',
    'minorUnit' => 8,
  ),
  'DATA' => 
  array (
    'symbol' => 'DATA',
    'minorUnit' => 8,
  ),
  'DCR' => 
  array (
    'symbol' => 'DCR',
    'minorUnit' => 8,
  ),
  'DEGO' => 
  array (
    'symbol' => 'DEGO',
    'minorUnit' => 8,
  ),
  'DENT' => 
  array (
    'symbol' => 'DENT',
    'minorUnit' => 8,
  ),
  'DEXE' => 
  array (
    'symbol' => 'DEXE',
    'minorUnit' => 8,
  ),
  'DF' => 
  array (
    'symbol' => 'DF',
    'minorUnit' => 8,
  ),
  'DGB' => 
  array (
    'symbol' => 'DGB',
    'minorUnit' => 8,
  ),
  'DIA' => 
  array (
    'symbol' => 'DIA',
    'minorUnit' => 8,
  ),
  'DODO' => 
  array (
    'symbol' => 'DODO',
    'minorUnit' => 8,
  ),
  'DOGE' => 
  array (
    'symbol' => 'DOGE',
    'minorUnit' => 8,
  ),
  'DOGS' => 
  array (
    'symbol' => 'DOGS',
    'minorUnit' => 8,
  ),
  'DOT' => 
  array (
    'symbol' => 'DOT',
    'minorUnit' => 8,
  ),
  'DUSK' => 
  array (
    'symbol' => 'DUSK',
    'minorUnit' => 8,
  ),
  'DYDX' => 
  array (
    'symbol' => 'DYDX',
    'minorUnit' => 8,
  ),
  'DYM' => 
  array (
    'symbol' => 'DYM',
    'minorUnit' => 8,
  ),
  'EDU' => 
  array (
    'symbol' => 'EDU',
    'minorUnit' => 8,
  ),
  'EGLD' => 
  array (
    'symbol' => 'EGLD',
    'minorUnit' => 8,
  ),
  'EIGEN' => 
  array (
    'symbol' => 'EIGEN',
    'minorUnit' => 8,
  ),
  'ENA' => 
  array (
    'symbol' => 'ENA',
    'minorUnit' => 8,
  ),
  'ENJ' => 
  array (
    'symbol' => 'ENJ',
    'minorUnit' => 8,
  ),
  'ENS' => 
  array (
    'symbol' => 'ENS',
    'minorUnit' => 8,
  ),
  'EPIC' => 
  array (
    'symbol' => 'EPIC',
    'minorUnit' => 8,
  ),
  'ETC' => 
  array (
    'symbol' => 'ETC',
    'minorUnit' => 8,
  ),
  'ETH' => 
  array (
    'symbol' => 'ETH',
    'minorUnit' => 8,
  ),
  'ETHFI' => 
  array (
    'symbol' => 'ETHFI',
    'minorUnit' => 8,
  ),
  'EURI' => 
  array (
    'symbol' => 'EURI',
    'minorUnit' => 8,
  ),
  'FARM' => 
  array (
    'symbol' => 'FARM',
    'minorUnit' => 8,
  ),
  'FDUSD' => 
  array (
    'symbol' => 'FDUSD',
    'minorUnit' => 8,
  ),
  'FET' => 
  array (
    'symbol' => 'FET',
    'minorUnit' => 8,
  ),
  'FIDA' => 
  array (
    'symbol' => 'FIDA',
    'minorUnit' => 8,
  ),
  'FIL' => 
  array (
    'symbol' => 'FIL',
    'minorUnit' => 8,
  ),
  'FIO' => 
  array (
    'symbol' => 'FIO',
    'minorUnit' => 8,
  ),
  'FIS' => 
  array (
    'symbol' => 'FIS',
    'minorUnit' => 8,
  ),
  'FLM' => 
  array (
    'symbol' => 'FLM',
    'minorUnit' => 8,
  ),
  'FLOKI' => 
  array (
    'symbol' => 'FLOKI',
    'minorUnit' => 8,
  ),
  'FLOW' => 
  array (
    'symbol' => 'FLOW',
    'minorUnit' => 8,
  ),
  'FLUX' => 
  array (
    'symbol' => 'FLUX',
    'minorUnit' => 8,
  ),
  'FORM' => 
  array (
    'symbol' => 'FORM',
    'minorUnit' => 8,
  ),
  'FORTH' => 
  array (
    'symbol' => 'FORTH',
    'minorUnit' => 8,
  ),
  'FTT' => 
  array (
    'symbol' => 'FTT',
    'minorUnit' => 8,
  ),
  'FUN' => 
  array (
    'symbol' => 'FUN',
    'minorUnit' => 8,
  ),
  'FXS' => 
  array (
    'symbol' => 'FXS',
    'minorUnit' => 8,
  ),
  'G' => 
  array (
    'symbol' => 'G',
    'minorUnit' => 8,
  ),
  'GALA' => 
  array (
    'symbol' => 'GALA',
    'minorUnit' => 8,
  ),
  'GAS' => 
  array (
    'symbol' => 'GAS',
    'minorUnit' => 8,
  ),
  'GHST' => 
  array (
    'symbol' => 'GHST',
    'minorUnit' => 8,
  ),
  'GLM' => 
  array (
    'symbol' => 'GLM',
    'minorUnit' => 8,
  ),
  'GLMR' => 
  array (
    'symbol' => 'GLMR',
    'minorUnit' => 8,
  ),
  'GMT' => 
  array (
    'symbol' => 'GMT',
    'minorUnit' => 8,
  ),
  'GMX' => 
  array (
    'symbol' => 'GMX',
    'minorUnit' => 8,
  ),
  'GNO' => 
  array (
    'symbol' => 'GNO',
    'minorUnit' => 8,
  ),
  'GNS' => 
  array (
    'symbol' => 'GNS',
    'minorUnit' => 8,
  ),
  'GPS' => 
  array (
    'symbol' => 'GPS',
    'minorUnit' => 8,
  ),
  'GRT' => 
  array (
    'symbol' => 'GRT',
    'minorUnit' => 8,
  ),
  'GTC' => 
  array (
    'symbol' => 'GTC',
    'minorUnit' => 8,
  ),
  'GUN' => 
  array (
    'symbol' => 'GUN',
    'minorUnit' => 8,
  ),
  'HAEDAL' => 
  array (
    'symbol' => 'HAEDAL',
    'minorUnit' => 8,
  ),
  'HBAR' => 
  array (
    'symbol' => 'HBAR',
    'minorUnit' => 8,
  ),
  'HEI' => 
  array (
    'symbol' => 'HEI',
    'minorUnit' => 8,
  ),
  'HFT' => 
  array (
    'symbol' => 'HFT',
    'minorUnit' => 8,
  ),
  'HIFI' => 
  array (
    'symbol' => 'HIFI',
    'minorUnit' => 8,
  ),
  'HIGH' => 
  array (
    'symbol' => 'HIGH',
    'minorUnit' => 8,
  ),
  'HIVE' => 
  array (
    'symbol' => 'HIVE',
    'minorUnit' => 8,
  ),
  'HMSTR' => 
  array (
    'symbol' => 'HMSTR',
    'minorUnit' => 8,
  ),
  'HOOK' => 
  array (
    'symbol' => 'HOOK',
    'minorUnit' => 8,
  ),
  'HOT' => 
  array (
    'symbol' => 'HOT',
    'minorUnit' => 8,
  ),
  'HUMA' => 
  array (
    'symbol' => 'HUMA',
    'minorUnit' => 8,
  ),
  'HYPER' => 
  array (
    'symbol' => 'HYPER',
    'minorUnit' => 8,
  ),
  'ICP' => 
  array (
    'symbol' => 'ICP',
    'minorUnit' => 8,
  ),
  'ICX' => 
  array (
    'symbol' => 'ICX',
    'minorUnit' => 8,
  ),
  'ID' => 
  array (
    'symbol' => 'ID',
    'minorUnit' => 8,
  ),
  'IDEX' => 
  array (
    'symbol' => 'IDEX',
    'minorUnit' => 8,
  ),
  'ILV' => 
  array (
    'symbol' => 'ILV',
    'minorUnit' => 8,
  ),
  'IMX' => 
  array (
    'symbol' => 'IMX',
    'minorUnit' => 8,
  ),
  'INIT' => 
  array (
    'symbol' => 'INIT',
    'minorUnit' => 8,
  ),
  'INJ' => 
  array (
    'symbol' => 'INJ',
    'minorUnit' => 8,
  ),
  'IO' => 
  array (
    'symbol' => 'IO',
    'minorUnit' => 8,
  ),
  'IOST' => 
  array (
    'symbol' => 'IOST',
    'minorUnit' => 8,
  ),
  'IOTA' => 
  array (
    'symbol' => 'IOTA',
    'minorUnit' => 8,
  ),
  'IOTX' => 
  array (
    'symbol' => 'IOTX',
    'minorUnit' => 8,
  ),
  'IQ' => 
  array (
    'symbol' => 'IQ',
    'minorUnit' => 8,
  ),
  'JASMY' => 
  array (
    'symbol' => 'JASMY',
    'minorUnit' => 8,
  ),
  'JOE' => 
  array (
    'symbol' => 'JOE',
    'minorUnit' => 8,
  ),
  'JPY' => 
  array (
    'symbol' => 'JPY',
    'minorUnit' => 8,
  ),
  'JST' => 
  array (
    'symbol' => 'JST',
    'minorUnit' => 8,
  ),
  'JTO' => 
  array (
    'symbol' => 'JTO',
    'minorUnit' => 8,
  ),
  'JUP' => 
  array (
    'symbol' => 'JUP',
    'minorUnit' => 8,
  ),
  'JUV' => 
  array (
    'symbol' => 'JUV',
    'minorUnit' => 8,
  ),
  'KAIA' => 
  array (
    'symbol' => 'KAIA',
    'minorUnit' => 8,
  ),
  'KAITO' => 
  array (
    'symbol' => 'KAITO',
    'minorUnit' => 8,
  ),
  'KAVA' => 
  array (
    'symbol' => 'KAVA',
    'minorUnit' => 8,
  ),
  'KDA' => 
  array (
    'symbol' => 'KDA',
    'minorUnit' => 8,
  ),
  'KERNEL' => 
  array (
    'symbol' => 'KERNEL',
    'minorUnit' => 8,
  ),
  'KMD' => 
  array (
    'symbol' => 'KMD',
    'minorUnit' => 8,
  ),
  'KMNO' => 
  array (
    'symbol' => 'KMNO',
    'minorUnit' => 8,
  ),
  'KNC' => 
  array (
    'symbol' => 'KNC',
    'minorUnit' => 8,
  ),
  'KSM' => 
  array (
    'symbol' => 'KSM',
    'minorUnit' => 8,
  ),
  'LAYER' => 
  array (
    'symbol' => 'LAYER',
    'minorUnit' => 8,
  ),
  'LAZIO' => 
  array (
    'symbol' => 'LAZIO',
    'minorUnit' => 8,
  ),
  'LDO' => 
  array (
    'symbol' => 'LDO',
    'minorUnit' => 8,
  ),
  'LEVER' => 
  array (
    'symbol' => 'LEVER',
    'minorUnit' => 8,
  ),
  'LINK' => 
  array (
    'symbol' => 'LINK',
    'minorUnit' => 8,
  ),
  'LISTA' => 
  array (
    'symbol' => 'LISTA',
    'minorUnit' => 8,
  ),
  'LOKA' => 
  array (
    'symbol' => 'LOKA',
    'minorUnit' => 8,
  ),
  'LPT' => 
  array (
    'symbol' => 'LPT',
    'minorUnit' => 8,
  ),
  'LQTY' => 
  array (
    'symbol' => 'LQTY',
    'minorUnit' => 8,
  ),
  'LRC' => 
  array (
    'symbol' => 'LRC',
    'minorUnit' => 8,
  ),
  'LSK' => 
  array (
    'symbol' => 'LSK',
    'minorUnit' => 8,
  ),
  'LTC' => 
  array (
    'symbol' => 'LTC',
    'minorUnit' => 8,
  ),
  'LTO' => 
  array (
    'symbol' => 'LTO',
    'minorUnit' => 8,
  ),
  'LUMIA' => 
  array (
    'symbol' => 'LUMIA',
    'minorUnit' => 8,
  ),
  'LUNA' => 
  array (
    'symbol' => 'LUNA',
    'minorUnit' => 8,
  ),
  'LUNC' => 
  array (
    'symbol' => 'LUNC',
    'minorUnit' => 8,
  ),
  'MAGIC' => 
  array (
    'symbol' => 'MAGIC',
    'minorUnit' => 8,
  ),
  'MANA' => 
  array (
    'symbol' => 'MANA',
    'minorUnit' => 8,
  ),
  'MANTA' => 
  array (
    'symbol' => 'MANTA',
    'minorUnit' => 8,
  ),
  'MASK' => 
  array (
    'symbol' => 'MASK',
    'minorUnit' => 8,
  ),
  'MAV' => 
  array (
    'symbol' => 'MAV',
    'minorUnit' => 8,
  ),
  'MBL' => 
  array (
    'symbol' => 'MBL',
    'minorUnit' => 8,
  ),
  'MBOX' => 
  array (
    'symbol' => 'MBOX',
    'minorUnit' => 8,
  ),
  'MDT' => 
  array (
    'symbol' => 'MDT',
    'minorUnit' => 8,
  ),
  'ME' => 
  array (
    'symbol' => 'ME',
    'minorUnit' => 8,
  ),
  'MEME' => 
  array (
    'symbol' => 'MEME',
    'minorUnit' => 8,
  ),
  'METIS' => 
  array (
    'symbol' => 'METIS',
    'minorUnit' => 8,
  ),
  'MINA' => 
  array (
    'symbol' => 'MINA',
    'minorUnit' => 8,
  ),
  'MKR' => 
  array (
    'symbol' => 'MKR',
    'minorUnit' => 8,
  ),
  'MLN' => 
  array (
    'symbol' => 'MLN',
    'minorUnit' => 8,
  ),
  'MOVE' => 
  array (
    'symbol' => 'MOVE',
    'minorUnit' => 8,
  ),
  'MOVR' => 
  array (
    'symbol' => 'MOVR',
    'minorUnit' => 8,
  ),
  'MTL' => 
  array (
    'symbol' => 'MTL',
    'minorUnit' => 8,
  ),
  'MUBARAK' => 
  array (
    'symbol' => 'MUBARAK',
    'minorUnit' => 8,
  ),
  'MXN' => 
  array (
    'symbol' => 'MXN',
    'minorUnit' => 8,
  ),
  'NEAR' => 
  array (
    'symbol' => 'NEAR',
    'minorUnit' => 8,
  ),
  'NEIRO' => 
  array (
    'symbol' => 'NEIRO',
    'minorUnit' => 8,
  ),
  'NEO' => 
  array (
    'symbol' => 'NEO',
    'minorUnit' => 8,
  ),
  'NEXO' => 
  array (
    'symbol' => 'NEXO',
    'minorUnit' => 8,
  ),
  'NFP' => 
  array (
    'symbol' => 'NFP',
    'minorUnit' => 8,
  ),
  'NIL' => 
  array (
    'symbol' => 'NIL',
    'minorUnit' => 8,
  ),
  'NKN' => 
  array (
    'symbol' => 'NKN',
    'minorUnit' => 8,
  ),
  'NMR' => 
  array (
    'symbol' => 'NMR',
    'minorUnit' => 8,
  ),
  'NOT' => 
  array (
    'symbol' => 'NOT',
    'minorUnit' => 8,
  ),
  'NTRN' => 
  array (
    'symbol' => 'NTRN',
    'minorUnit' => 8,
  ),
  'NXPC' => 
  array (
    'symbol' => 'NXPC',
    'minorUnit' => 8,
  ),
  'OG' => 
  array (
    'symbol' => 'OG',
    'minorUnit' => 8,
  ),
  'OGN' => 
  array (
    'symbol' => 'OGN',
    'minorUnit' => 8,
  ),
  'OM' => 
  array (
    'symbol' => 'OM',
    'minorUnit' => 8,
  ),
  'OMNI' => 
  array (
    'symbol' => 'OMNI',
    'minorUnit' => 8,
  ),
  'ONDO' => 
  array (
    'symbol' => 'ONDO',
    'minorUnit' => 8,
  ),
  'ONE' => 
  array (
    'symbol' => 'ONE',
    'minorUnit' => 8,
  ),
  'ONG' => 
  array (
    'symbol' => 'ONG',
    'minorUnit' => 8,
  ),
  'ONT' => 
  array (
    'symbol' => 'ONT',
    'minorUnit' => 8,
  ),
  'OP' => 
  array (
    'symbol' => 'OP',
    'minorUnit' => 8,
  ),
  'ORCA' => 
  array (
    'symbol' => 'ORCA',
    'minorUnit' => 8,
  ),
  'ORDI' => 
  array (
    'symbol' => 'ORDI',
    'minorUnit' => 8,
  ),
  'OSMO' => 
  array (
    'symbol' => 'OSMO',
    'minorUnit' => 8,
  ),
  'OXT' => 
  array (
    'symbol' => 'OXT',
    'minorUnit' => 8,
  ),
  'PARTI' => 
  array (
    'symbol' => 'PARTI',
    'minorUnit' => 8,
  ),
  'PAXG' => 
  array (
    'symbol' => 'PAXG',
    'minorUnit' => 8,
  ),
  'PENDLE' => 
  array (
    'symbol' => 'PENDLE',
    'minorUnit' => 8,
  ),
  'PENGU' => 
  array (
    'symbol' => 'PENGU',
    'minorUnit' => 8,
  ),
  'PEOPLE' => 
  array (
    'symbol' => 'PEOPLE',
    'minorUnit' => 8,
  ),
  'PEPE' => 
  array (
    'symbol' => 'PEPE',
    'minorUnit' => 8,
  ),
  'PERP' => 
  array (
    'symbol' => 'PERP',
    'minorUnit' => 8,
  ),
  'PHA' => 
  array (
    'symbol' => 'PHA',
    'minorUnit' => 8,
  ),
  'PHB' => 
  array (
    'symbol' => 'PHB',
    'minorUnit' => 8,
  ),
  'PIVX' => 
  array (
    'symbol' => 'PIVX',
    'minorUnit' => 8,
  ),
  'PIXEL' => 
  array (
    'symbol' => 'PIXEL',
    'minorUnit' => 8,
  ),
  'PNUT' => 
  array (
    'symbol' => 'PNUT',
    'minorUnit' => 8,
  ),
  'POL' => 
  array (
    'symbol' => 'POL',
    'minorUnit' => 8,
  ),
  'POLYX' => 
  array (
    'symbol' => 'POLYX',
    'minorUnit' => 8,
  ),
  'POND' => 
  array (
    'symbol' => 'POND',
    'minorUnit' => 8,
  ),
  'PORTAL' => 
  array (
    'symbol' => 'PORTAL',
    'minorUnit' => 8,
  ),
  'PORTO' => 
  array (
    'symbol' => 'PORTO',
    'minorUnit' => 8,
  ),
  'POWR' => 
  array (
    'symbol' => 'POWR',
    'minorUnit' => 8,
  ),
  'PROM' => 
  array (
    'symbol' => 'PROM',
    'minorUnit' => 8,
  ),
  'PSG' => 
  array (
    'symbol' => 'PSG',
    'minorUnit' => 8,
  ),
  'PUNDIX' => 
  array (
    'symbol' => 'PUNDIX',
    'minorUnit' => 8,
  ),
  'PYR' => 
  array (
    'symbol' => 'PYR',
    'minorUnit' => 8,
  ),
  'PYTH' => 
  array (
    'symbol' => 'PYTH',
    'minorUnit' => 8,
  ),
  'QI' => 
  array (
    'symbol' => 'QI',
    'minorUnit' => 8,
  ),
  'QKC' => 
  array (
    'symbol' => 'QKC',
    'minorUnit' => 8,
  ),
  'QNT' => 
  array (
    'symbol' => 'QNT',
    'minorUnit' => 8,
  ),
  'QTUM' => 
  array (
    'symbol' => 'QTUM',
    'minorUnit' => 8,
  ),
  'QUICK' => 
  array (
    'symbol' => 'QUICK',
    'minorUnit' => 8,
  ),
  'RAD' => 
  array (
    'symbol' => 'RAD',
    'minorUnit' => 8,
  ),
  'RARE' => 
  array (
    'symbol' => 'RARE',
    'minorUnit' => 8,
  ),
  'RAY' => 
  array (
    'symbol' => 'RAY',
    'minorUnit' => 8,
  ),
  'RDNT' => 
  array (
    'symbol' => 'RDNT',
    'minorUnit' => 8,
  ),
  'RED' => 
  array (
    'symbol' => 'RED',
    'minorUnit' => 8,
  ),
  'REI' => 
  array (
    'symbol' => 'REI',
    'minorUnit' => 8,
  ),
  'RENDER' => 
  array (
    'symbol' => 'RENDER',
    'minorUnit' => 8,
  ),
  'REQ' => 
  array (
    'symbol' => 'REQ',
    'minorUnit' => 8,
  ),
  'REZ' => 
  array (
    'symbol' => 'REZ',
    'minorUnit' => 8,
  ),
  'RIF' => 
  array (
    'symbol' => 'RIF',
    'minorUnit' => 8,
  ),
  'RLC' => 
  array (
    'symbol' => 'RLC',
    'minorUnit' => 8,
  ),
  'RONIN' => 
  array (
    'symbol' => 'RONIN',
    'minorUnit' => 8,
  ),
  'ROSE' => 
  array (
    'symbol' => 'ROSE',
    'minorUnit' => 8,
  ),
  'RPL' => 
  array (
    'symbol' => 'RPL',
    'minorUnit' => 8,
  ),
  'RSR' => 
  array (
    'symbol' => 'RSR',
    'minorUnit' => 8,
  ),
  'RUNE' => 
  array (
    'symbol' => 'RUNE',
    'minorUnit' => 8,
  ),
  'RVN' => 
  array (
    'symbol' => 'RVN',
    'minorUnit' => 8,
  ),
  'S' => 
  array (
    'symbol' => 'S',
    'minorUnit' => 8,
  ),
  'SAGA' => 
  array (
    'symbol' => 'SAGA',
    'minorUnit' => 8,
  ),
  'SAND' => 
  array (
    'symbol' => 'SAND',
    'minorUnit' => 8,
  ),
  'SANTOS' => 
  array (
    'symbol' => 'SANTOS',
    'minorUnit' => 8,
  ),
  'SC' => 
  array (
    'symbol' => 'SC',
    'minorUnit' => 8,
  ),
  'SCR' => 
  array (
    'symbol' => 'SCR',
    'minorUnit' => 8,
  ),
  'SCRT' => 
  array (
    'symbol' => 'SCRT',
    'minorUnit' => 8,
  ),
  'SEI' => 
  array (
    'symbol' => 'SEI',
    'minorUnit' => 8,
  ),
  'SFP' => 
  array (
    'symbol' => 'SFP',
    'minorUnit' => 8,
  ),
  'SHELL' => 
  array (
    'symbol' => 'SHELL',
    'minorUnit' => 8,
  ),
  'SHIB' => 
  array (
    'symbol' => 'SHIB',
    'minorUnit' => 8,
  ),
  'SIGN' => 
  array (
    'symbol' => 'SIGN',
    'minorUnit' => 8,
  ),
  'SKL' => 
  array (
    'symbol' => 'SKL',
    'minorUnit' => 8,
  ),
  'SLF' => 
  array (
    'symbol' => 'SLF',
    'minorUnit' => 8,
  ),
  'SLP' => 
  array (
    'symbol' => 'SLP',
    'minorUnit' => 8,
  ),
  'SNX' => 
  array (
    'symbol' => 'SNX',
    'minorUnit' => 8,
  ),
  'SOL' => 
  array (
    'symbol' => 'SOL',
    'minorUnit' => 8,
  ),
  'SOLV' => 
  array (
    'symbol' => 'SOLV',
    'minorUnit' => 8,
  ),
  'SOPH' => 
  array (
    'symbol' => 'SOPH',
    'minorUnit' => 8,
  ),
  'SPELL' => 
  array (
    'symbol' => 'SPELL',
    'minorUnit' => 8,
  ),
  'SSV' => 
  array (
    'symbol' => 'SSV',
    'minorUnit' => 8,
  ),
  'STEEM' => 
  array (
    'symbol' => 'STEEM',
    'minorUnit' => 8,
  ),
  'STG' => 
  array (
    'symbol' => 'STG',
    'minorUnit' => 8,
  ),
  'STO' => 
  array (
    'symbol' => 'STO',
    'minorUnit' => 8,
  ),
  'STORJ' => 
  array (
    'symbol' => 'STORJ',
    'minorUnit' => 8,
  ),
  'STRAX' => 
  array (
    'symbol' => 'STRAX',
    'minorUnit' => 8,
  ),
  'STRK' => 
  array (
    'symbol' => 'STRK',
    'minorUnit' => 8,
  ),
  'STX' => 
  array (
    'symbol' => 'STX',
    'minorUnit' => 8,
  ),
  'SUI' => 
  array (
    'symbol' => 'SUI',
    'minorUnit' => 8,
  ),
  'SUN' => 
  array (
    'symbol' => 'SUN',
    'minorUnit' => 8,
  ),
  'SUPER' => 
  array (
    'symbol' => 'SUPER',
    'minorUnit' => 8,
  ),
  'SUSHI' => 
  array (
    'symbol' => 'SUSHI',
    'minorUnit' => 8,
  ),
  'SXP' => 
  array (
    'symbol' => 'SXP',
    'minorUnit' => 8,
  ),
  'SXT' => 
  array (
    'symbol' => 'SXT',
    'minorUnit' => 8,
  ),
  'SYN' => 
  array (
    'symbol' => 'SYN',
    'minorUnit' => 8,
  ),
  'SYRUP' => 
  array (
    'symbol' => 'SYRUP',
    'minorUnit' => 8,
  ),
  'SYS' => 
  array (
    'symbol' => 'SYS',
    'minorUnit' => 8,
  ),
  'T' => 
  array (
    'symbol' => 'T',
    'minorUnit' => 8,
  ),
  'TAO' => 
  array (
    'symbol' => 'TAO',
    'minorUnit' => 8,
  ),
  'TFUEL' => 
  array (
    'symbol' => 'TFUEL',
    'minorUnit' => 8,
  ),
  'THE' => 
  array (
    'symbol' => 'THE',
    'minorUnit' => 8,
  ),
  'THETA' => 
  array (
    'symbol' => 'THETA',
    'minorUnit' => 8,
  ),
  'TIA' => 
  array (
    'symbol' => 'TIA',
    'minorUnit' => 8,
  ),
  'TKO' => 
  array (
    'symbol' => 'TKO',
    'minorUnit' => 8,
  ),
  'TLM' => 
  array (
    'symbol' => 'TLM',
    'minorUnit' => 8,
  ),
  'TNSR' => 
  array (
    'symbol' => 'TNSR',
    'minorUnit' => 8,
  ),
  'TON' => 
  array (
    'symbol' => 'TON',
    'minorUnit' => 8,
  ),
  'TRB' => 
  array (
    'symbol' => 'TRB',
    'minorUnit' => 8,
  ),
  'TRU' => 
  array (
    'symbol' => 'TRU',
    'minorUnit' => 8,
  ),
  'TRUMP' => 
  array (
    'symbol' => 'TRUMP',
    'minorUnit' => 8,
  ),
  'TRX' => 
  array (
    'symbol' => 'TRX',
    'minorUnit' => 8,
  ),
  'TST' => 
  array (
    'symbol' => 'TST',
    'minorUnit' => 8,
  ),
  'TURBO' => 
  array (
    'symbol' => 'TURBO',
    'minorUnit' => 8,
  ),
  'TUSD' => 
  array (
    'symbol' => 'TUSD',
    'minorUnit' => 8,
  ),
  'TUT' => 
  array (
    'symbol' => 'TUT',
    'minorUnit' => 8,
  ),
  'TWT' => 
  array (
    'symbol' => 'TWT',
    'minorUnit' => 8,
  ),
  'UMA' => 
  array (
    'symbol' => 'UMA',
    'minorUnit' => 8,
  ),
  'UNI' => 
  array (
    'symbol' => 'UNI',
    'minorUnit' => 8,
  ),
  'USD1' => 
  array (
    'symbol' => 'USD1',
    'minorUnit' => 8,
  ),
  'USDC' => 
  array (
    'symbol' => 'USDC',
    'minorUnit' => 8,
  ),
  'USDP' => 
  array (
    'symbol' => 'USDP',
    'minorUnit' => 8,
  ),
  'USDT' => 
  array (
    'symbol' => 'USDT',
    'minorUnit' => 8,
  ),
  'USTC' => 
  array (
    'symbol' => 'USTC',
    'minorUnit' => 8,
  ),
  'USUAL' => 
  array (
    'symbol' => 'USUAL',
    'minorUnit' => 8,
  ),
  'UTK' => 
  array (
    'symbol' => 'UTK',
    'minorUnit' => 8,
  ),
  'VANA' => 
  array (
    'symbol' => 'VANA',
    'minorUnit' => 8,
  ),
  'VANRY' => 
  array (
    'symbol' => 'VANRY',
    'minorUnit' => 8,
  ),
  'VELODROME' => 
  array (
    'symbol' => 'VELODROME',
    'minorUnit' => 8,
  ),
  'VET' => 
  array (
    'symbol' => 'VET',
    'minorUnit' => 8,
  ),
  'VIC' => 
  array (
    'symbol' => 'VIC',
    'minorUnit' => 8,
  ),
  'VIRTUAL' => 
  array (
    'symbol' => 'VIRTUAL',
    'minorUnit' => 8,
  ),
  'VOXEL' => 
  array (
    'symbol' => 'VOXEL',
    'minorUnit' => 8,
  ),
  'VTHO' => 
  array (
    'symbol' => 'VTHO',
    'minorUnit' => 8,
  ),
  'W' => 
  array (
    'symbol' => 'W',
    'minorUnit' => 8,
  ),
  'WAN' => 
  array (
    'symbol' => 'WAN',
    'minorUnit' => 8,
  ),
  'WAXP' => 
  array (
    'symbol' => 'WAXP',
    'minorUnit' => 8,
  ),
  'WBETH' => 
  array (
    'symbol' => 'WBETH',
    'minorUnit' => 8,
  ),
  'WBTC' => 
  array (
    'symbol' => 'WBTC',
    'minorUnit' => 8,
  ),
  'WCT' => 
  array (
    'symbol' => 'WCT',
    'minorUnit' => 8,
  ),
  'WIF' => 
  array (
    'symbol' => 'WIF',
    'minorUnit' => 8,
  ),
  'WIN' => 
  array (
    'symbol' => 'WIN',
    'minorUnit' => 8,
  ),
  'WLD' => 
  array (
    'symbol' => 'WLD',
    'minorUnit' => 8,
  ),
  'WOO' => 
  array (
    'symbol' => 'WOO',
    'minorUnit' => 8,
  ),
  'XAI' => 
  array (
    'symbol' => 'XAI',
    'minorUnit' => 8,
  ),
  'XEC' => 
  array (
    'symbol' => 'XEC',
    'minorUnit' => 8,
  ),
  'XLM' => 
  array (
    'symbol' => 'XLM',
    'minorUnit' => 8,
  ),
  'XNO' => 
  array (
    'symbol' => 'XNO',
    'minorUnit' => 8,
  ),
  'XRP' => 
  array (
    'symbol' => 'XRP',
    'minorUnit' => 8,
  ),
  'XTZ' => 
  array (
    'symbol' => 'XTZ',
    'minorUnit' => 8,
  ),
  'XUSD' => 
  array (
    'symbol' => 'XUSD',
    'minorUnit' => 8,
  ),
  'XVG' => 
  array (
    'symbol' => 'XVG',
    'minorUnit' => 8,
  ),
  'XVS' => 
  array (
    'symbol' => 'XVS',
    'minorUnit' => 8,
  ),
  'YFI' => 
  array (
    'symbol' => 'YFI',
    'minorUnit' => 8,
  ),
  'YGG' => 
  array (
    'symbol' => 'YGG',
    'minorUnit' => 8,
  ),
  'ZEC' => 
  array (
    'symbol' => 'ZEC',
    'minorUnit' => 8,
  ),
  'ZEN' => 
  array (
    'symbol' => 'ZEN',
    'minorUnit' => 8,
  ),
  'ZIL' => 
  array (
    'symbol' => 'ZIL',
    'minorUnit' => 8,
  ),
  'ZK' => 
  array (
    'symbol' => 'ZK',
    'minorUnit' => 8,
  ),
  'ZRO' => 
  array (
    'symbol' => 'ZRO',
    'minorUnit' => 8,
  ),
  'ZRX' => 
  array (
    'symbol' => 'ZRX',
    'minorUnit' => 8,
  ),
);