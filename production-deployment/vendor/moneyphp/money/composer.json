{"name": "moneyphp/money", "description": "PHP implementation of Fowler's Money pattern", "license": "MIT", "keywords": ["money", "vo", "value object"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://moneyphp.org", "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-bcmath": "*", "ext-filter": "*", "ext-json": "*"}, "require-dev": {"ext-gmp": "*", "ext-intl": "*", "cache/taggable-cache": "^1.1.0", "doctrine/coding-standard": "^12.0", "doctrine/instantiator": "^1.5.0 || ^2.0", "florianv/exchanger": "^2.8.1", "florianv/swap": "^4.3.0", "moneyphp/crypto-currencies": "^1.1.0", "moneyphp/iso-currencies": "^3.4", "php-http/message": "^1.16.0", "php-http/mock-client": "^1.6.0", "phpbench/phpbench": "^1.2.5", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1.9", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5.9", "psr/cache": "^1.0.1 || ^2.0 || ^3.0", "ticketswap/phpstan-error-formatter": "^1.1"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Money\\": "src/"}}, "autoload-dev": {"psr-4": {"Benchmark\\Money\\": "benchmark/", "Tests\\Money\\": "tests/"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "infection/extension-installer": true, "php-http/discovery": false, "phpstan/extension-installer": true}, "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "scripts": {"benchmark": ["vendor/bin/phpbench run --retry-threshold=3 --iterations=15 --revs=1000  --warmup=2"], "clean": "rm -rf build/ vendor/", "test": ["vendor/bin/phpbench run", "vendor/bin/phpunit", "vendor/bin/phpstan", "vendor/bin/phpcs"], "test-coverage": ["vendor/bin/phpunit --coverage-text --coverage-clover=build/unit_coverage.xml"], "update-currencies": ["cp vendor/moneyphp/iso-currencies/resources/current.php resources/currency.php", "cp vendor/moneyphp/crypto-currencies/resources/binance.php resources/binance.php", "php resources/generate-money-factory.php", "php resources/generate-teller-factory.php"]}}