<?php

declare(strict_types=1);

namespace Money;

use Money\Currencies\ISOCurrencies;
use Money\Formatter\DecimalMoneyFormatter;
use Money\Parser\DecimalMoneyParser;

use function array_shift;

/**
 * This is a generated file. Do not edit it manually!
 *
 * @method static Teller ONETHOUSANDCAT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONETHOUSANDCHEEMS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONETHOUSANDSATS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONEINCH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONEMBABYDOGE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller A(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AAVE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ACX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ADA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ADX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AED(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AEUR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AEVO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AFN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AGLD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AIXBT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALCX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALGO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALICE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALPHA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALPINE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ALT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AMD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AMP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ANIME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ANKR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AOA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller APE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller API3(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller APT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARDR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARKM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARPA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ARS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ASR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ASTR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ATA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ATM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ATOM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AUCTION(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AUD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AUDIO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AVA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AVAX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AWE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AWG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AXL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AXS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller AZN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BABY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BAKE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BAM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BANANA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BANANAS31(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BAND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BAT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BBD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BCH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BDT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BEAMX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BERA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BGN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BHD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BICO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BIF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BIFI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BIGTIME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BIO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BLUR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BMD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BMT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BNB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BNSOL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BNT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BOB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BOME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BONK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BOV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BRL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BROCCOLI714(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BSD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BSW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BTN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BTTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BWP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BYN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller BZD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller C98(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CAD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CAKE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CATI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CDF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CELO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CELR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CETUS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CFX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CGPT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHESS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CHZ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CITY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CKB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CLF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CLP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CNY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COMP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COOKIE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COTI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller COW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CRC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CRV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CTK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CTSI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CUP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CVC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CVE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CVX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CYBER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller CZK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller D(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DAI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DASH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DATA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DCR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DEGO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DENT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DEXE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DGB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DIA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DJF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DKK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DODO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DOGE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DOGS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DOP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DOT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DUSK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DYDX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DYM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller DZD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EDU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EGLD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EGP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EIGEN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ENA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ENJ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ENS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EPIC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ERN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ETB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ETC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ETH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ETHFI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EUR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller EURI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FARM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FDUSD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FET(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FIDA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FIL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FIO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FIS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FJD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FKP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FLM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FLOKI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FLOW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FLUX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FORM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FORTH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FTT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FUN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller FXS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller G(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GALA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GAS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GBP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GHS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GHST(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GIP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GLM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GLMR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GMD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GMT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GMX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GNF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GNO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GNS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GPS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GRT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GTQ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GUN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller GYD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HAEDAL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HBAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HEI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HFT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HIFI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HIGH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HIVE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HKD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HMSTR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HNL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HOOK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HOT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HTG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HUF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HUMA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller HYPER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ICP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ICX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ID(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IDEX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IDR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ILS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ILV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IMX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller INIT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller INJ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller INR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IOST(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IOTA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IOTX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IQ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IQD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller IRR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ISK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JASMY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JMD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JOD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JOE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JPY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JST(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JTO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JUP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller JUV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KAIA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KAITO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KAVA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KDA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KERNEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KES(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KGS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KHR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KMD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KMF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KMNO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KNC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KPW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KRW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KSM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KWD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KYD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller KZT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LAK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LAYER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LAZIO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LBP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LDO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LEVER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LINK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LISTA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LKR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LOKA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LPT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LQTY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LRC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LRD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LSK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LSL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LTO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LUMIA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LUNA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LUNC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller LYD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MAD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MAGIC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MANA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MANTA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MASK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MAV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MBL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MBOX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MDL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MDT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MEME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller METIS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MGA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MINA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MKD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MKR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MLN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MMK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MNT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MOP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MOVE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MOVR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MRU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MTL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MUBARAK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MUR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MVR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MWK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MXN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MXV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MYR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller MZN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NAD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NEAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NEIRO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NEO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NEXO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NFP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NGN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NIL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NIO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NKN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NMR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NOK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NOT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NPR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NTRN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NXPC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller NZD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OGN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OMNI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OMR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONDO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ONT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ORCA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ORDI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OSMO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller OXT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PAB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PARTI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PAXG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PEN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PENDLE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PENGU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PEOPLE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PEPE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PERP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PGK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PHA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PHB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PHP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PIVX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PIXEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PKR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PLN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PNUT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller POL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller POLYX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller POND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PORTAL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PORTO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller POWR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PROM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PSG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PUNDIX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PYG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PYR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller PYTH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QKC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QNT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QTUM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller QUICK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RAD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RARE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RAY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RDNT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RED(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller REI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RENDER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller REQ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller REZ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RIF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RLC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RON(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RONIN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ROSE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RPL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RSD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RSR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RUB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RUNE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RVN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller RWF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller S(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SAGA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SAND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SANTOS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SBD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SCR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SCRT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SDG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SEI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SEK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SFP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SGD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SHELL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SHIB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SHP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SIGN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SKL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SLE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SLF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SLP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SNX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SOL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SOLV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SOPH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SOS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SPELL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SRD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SSP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SSV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STEEM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STORJ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STRAX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STRK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller STX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SUI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SUN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SUPER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SUSHI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SVC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SXP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SXT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SYN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SYP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SYRUP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SYS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller SZL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller T(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TAO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TFUEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller THB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller THE(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller THETA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TIA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TJS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TKO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TLM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TMT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TNSR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TON(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TOP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TRB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TRU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TRUMP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TRX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TRY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TST(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TTD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TURBO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TUSD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TUT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TWD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TWT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller TZS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UAH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UGX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UMA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UNI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USD1(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USDC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USDP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USDT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller USUAL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UTK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UYI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UYU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UYW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller UZS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VANA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VANRY(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VED(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VELODROME(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VES(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VET(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VIC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VIRTUAL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VND(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VOXEL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VTHO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller VUV(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller W(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WAN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WAXP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WBETH(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WBTC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WCT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WIF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WIN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WLD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WOO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller WST(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XAD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XAF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XAG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XAI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XAU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XBA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XBB(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XBC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XBD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XBT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XCD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XCG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XDR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XEC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XLM(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XNO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XOF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XPD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XPF(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XPT(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XRP(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XSU(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XTS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XTZ(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XUA(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XUSD(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XVG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XVS(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller XXX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller YER(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller YFI(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller YGG(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZAR(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZEC(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZEN(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZIL(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZK(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZMW(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZRO(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZRX(int $roundingMode = Money::ROUND_HALF_UP)
 * @method static Teller ZWG(int $roundingMode = Money::ROUND_HALF_UP)
 * @psalm-immutable
 */
trait TellerFactory
{
    /**
     * Convenience factory method for a Teller object.
     *
     * <code>
     * $teller = Teller::USD();
     * </code>
     *
     * @param non-empty-string          $method
     * @param array{0?: Money::ROUND_*} $arguments
     */
    public static function __callStatic(string $method, array $arguments): Teller
    {
        $currency     = new Currency($method);
        $currencies   = new ISOCurrencies();
        $parser       = new DecimalMoneyParser($currencies);
        $formatter    = new DecimalMoneyFormatter($currencies);
        $roundingMode = empty($arguments)
            ? Money::ROUND_HALF_UP
            : (int) array_shift($arguments);

        return new Teller(
            $currency,
            $parser,
            $formatter,
            $roundingMode
        );
    }
}
