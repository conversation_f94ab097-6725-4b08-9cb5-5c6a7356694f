<?php

declare(strict_types=1);

namespace Money;

use InvalidArgumentException;

/**
 * This is a generated file. Do not edit it manually!
 *
 * @method static Money ONETHOUSANDCAT(numeric-string|int $amount)
 * @method static Money ONETHOUSANDCHEEMS(numeric-string|int $amount)
 * @method static Money ONETHOUSANDSATS(numeric-string|int $amount)
 * @method static Money ONEINCH(numeric-string|int $amount)
 * @method static Money ONEMBABYDOGE(numeric-string|int $amount)
 * @method static Money A(numeric-string|int $amount)
 * @method static Money AAVE(numeric-string|int $amount)
 * @method static Money ACA(numeric-string|int $amount)
 * @method static Money ACE(numeric-string|int $amount)
 * @method static Money ACH(numeric-string|int $amount)
 * @method static Money ACM(numeric-string|int $amount)
 * @method static Money ACT(numeric-string|int $amount)
 * @method static Money ACX(numeric-string|int $amount)
 * @method static Money ADA(numeric-string|int $amount)
 * @method static Money ADX(numeric-string|int $amount)
 * @method static Money AED(numeric-string|int $amount)
 * @method static Money AEUR(numeric-string|int $amount)
 * @method static Money AEVO(numeric-string|int $amount)
 * @method static Money AFN(numeric-string|int $amount)
 * @method static Money AGLD(numeric-string|int $amount)
 * @method static Money AI(numeric-string|int $amount)
 * @method static Money AIXBT(numeric-string|int $amount)
 * @method static Money ALCX(numeric-string|int $amount)
 * @method static Money ALGO(numeric-string|int $amount)
 * @method static Money ALICE(numeric-string|int $amount)
 * @method static Money ALL(numeric-string|int $amount)
 * @method static Money ALPHA(numeric-string|int $amount)
 * @method static Money ALPINE(numeric-string|int $amount)
 * @method static Money ALT(numeric-string|int $amount)
 * @method static Money AMD(numeric-string|int $amount)
 * @method static Money AMP(numeric-string|int $amount)
 * @method static Money ANIME(numeric-string|int $amount)
 * @method static Money ANKR(numeric-string|int $amount)
 * @method static Money AOA(numeric-string|int $amount)
 * @method static Money APE(numeric-string|int $amount)
 * @method static Money API3(numeric-string|int $amount)
 * @method static Money APT(numeric-string|int $amount)
 * @method static Money AR(numeric-string|int $amount)
 * @method static Money ARB(numeric-string|int $amount)
 * @method static Money ARDR(numeric-string|int $amount)
 * @method static Money ARK(numeric-string|int $amount)
 * @method static Money ARKM(numeric-string|int $amount)
 * @method static Money ARPA(numeric-string|int $amount)
 * @method static Money ARS(numeric-string|int $amount)
 * @method static Money ASR(numeric-string|int $amount)
 * @method static Money ASTR(numeric-string|int $amount)
 * @method static Money ATA(numeric-string|int $amount)
 * @method static Money ATM(numeric-string|int $amount)
 * @method static Money ATOM(numeric-string|int $amount)
 * @method static Money AUCTION(numeric-string|int $amount)
 * @method static Money AUD(numeric-string|int $amount)
 * @method static Money AUDIO(numeric-string|int $amount)
 * @method static Money AVA(numeric-string|int $amount)
 * @method static Money AVAX(numeric-string|int $amount)
 * @method static Money AWE(numeric-string|int $amount)
 * @method static Money AWG(numeric-string|int $amount)
 * @method static Money AXL(numeric-string|int $amount)
 * @method static Money AXS(numeric-string|int $amount)
 * @method static Money AZN(numeric-string|int $amount)
 * @method static Money BABY(numeric-string|int $amount)
 * @method static Money BAKE(numeric-string|int $amount)
 * @method static Money BAM(numeric-string|int $amount)
 * @method static Money BANANA(numeric-string|int $amount)
 * @method static Money BANANAS31(numeric-string|int $amount)
 * @method static Money BAND(numeric-string|int $amount)
 * @method static Money BAR(numeric-string|int $amount)
 * @method static Money BAT(numeric-string|int $amount)
 * @method static Money BB(numeric-string|int $amount)
 * @method static Money BBD(numeric-string|int $amount)
 * @method static Money BCH(numeric-string|int $amount)
 * @method static Money BDT(numeric-string|int $amount)
 * @method static Money BEAMX(numeric-string|int $amount)
 * @method static Money BEL(numeric-string|int $amount)
 * @method static Money BERA(numeric-string|int $amount)
 * @method static Money BGN(numeric-string|int $amount)
 * @method static Money BHD(numeric-string|int $amount)
 * @method static Money BICO(numeric-string|int $amount)
 * @method static Money BIF(numeric-string|int $amount)
 * @method static Money BIFI(numeric-string|int $amount)
 * @method static Money BIGTIME(numeric-string|int $amount)
 * @method static Money BIO(numeric-string|int $amount)
 * @method static Money BLUR(numeric-string|int $amount)
 * @method static Money BMD(numeric-string|int $amount)
 * @method static Money BMT(numeric-string|int $amount)
 * @method static Money BNB(numeric-string|int $amount)
 * @method static Money BND(numeric-string|int $amount)
 * @method static Money BNSOL(numeric-string|int $amount)
 * @method static Money BNT(numeric-string|int $amount)
 * @method static Money BOB(numeric-string|int $amount)
 * @method static Money BOME(numeric-string|int $amount)
 * @method static Money BONK(numeric-string|int $amount)
 * @method static Money BOV(numeric-string|int $amount)
 * @method static Money BRL(numeric-string|int $amount)
 * @method static Money BROCCOLI714(numeric-string|int $amount)
 * @method static Money BSD(numeric-string|int $amount)
 * @method static Money BSW(numeric-string|int $amount)
 * @method static Money BTC(numeric-string|int $amount)
 * @method static Money BTN(numeric-string|int $amount)
 * @method static Money BTTC(numeric-string|int $amount)
 * @method static Money BWP(numeric-string|int $amount)
 * @method static Money BYN(numeric-string|int $amount)
 * @method static Money BZD(numeric-string|int $amount)
 * @method static Money C98(numeric-string|int $amount)
 * @method static Money CAD(numeric-string|int $amount)
 * @method static Money CAKE(numeric-string|int $amount)
 * @method static Money CATI(numeric-string|int $amount)
 * @method static Money CDF(numeric-string|int $amount)
 * @method static Money CELO(numeric-string|int $amount)
 * @method static Money CELR(numeric-string|int $amount)
 * @method static Money CETUS(numeric-string|int $amount)
 * @method static Money CFX(numeric-string|int $amount)
 * @method static Money CGPT(numeric-string|int $amount)
 * @method static Money CHE(numeric-string|int $amount)
 * @method static Money CHESS(numeric-string|int $amount)
 * @method static Money CHF(numeric-string|int $amount)
 * @method static Money CHR(numeric-string|int $amount)
 * @method static Money CHW(numeric-string|int $amount)
 * @method static Money CHZ(numeric-string|int $amount)
 * @method static Money CITY(numeric-string|int $amount)
 * @method static Money CKB(numeric-string|int $amount)
 * @method static Money CLF(numeric-string|int $amount)
 * @method static Money CLP(numeric-string|int $amount)
 * @method static Money CNY(numeric-string|int $amount)
 * @method static Money COMP(numeric-string|int $amount)
 * @method static Money COOKIE(numeric-string|int $amount)
 * @method static Money COP(numeric-string|int $amount)
 * @method static Money COS(numeric-string|int $amount)
 * @method static Money COTI(numeric-string|int $amount)
 * @method static Money COU(numeric-string|int $amount)
 * @method static Money COW(numeric-string|int $amount)
 * @method static Money CRC(numeric-string|int $amount)
 * @method static Money CRV(numeric-string|int $amount)
 * @method static Money CTK(numeric-string|int $amount)
 * @method static Money CTSI(numeric-string|int $amount)
 * @method static Money CUP(numeric-string|int $amount)
 * @method static Money CVC(numeric-string|int $amount)
 * @method static Money CVE(numeric-string|int $amount)
 * @method static Money CVX(numeric-string|int $amount)
 * @method static Money CYBER(numeric-string|int $amount)
 * @method static Money CZK(numeric-string|int $amount)
 * @method static Money D(numeric-string|int $amount)
 * @method static Money DAI(numeric-string|int $amount)
 * @method static Money DASH(numeric-string|int $amount)
 * @method static Money DATA(numeric-string|int $amount)
 * @method static Money DCR(numeric-string|int $amount)
 * @method static Money DEGO(numeric-string|int $amount)
 * @method static Money DENT(numeric-string|int $amount)
 * @method static Money DEXE(numeric-string|int $amount)
 * @method static Money DF(numeric-string|int $amount)
 * @method static Money DGB(numeric-string|int $amount)
 * @method static Money DIA(numeric-string|int $amount)
 * @method static Money DJF(numeric-string|int $amount)
 * @method static Money DKK(numeric-string|int $amount)
 * @method static Money DODO(numeric-string|int $amount)
 * @method static Money DOGE(numeric-string|int $amount)
 * @method static Money DOGS(numeric-string|int $amount)
 * @method static Money DOP(numeric-string|int $amount)
 * @method static Money DOT(numeric-string|int $amount)
 * @method static Money DUSK(numeric-string|int $amount)
 * @method static Money DYDX(numeric-string|int $amount)
 * @method static Money DYM(numeric-string|int $amount)
 * @method static Money DZD(numeric-string|int $amount)
 * @method static Money EDU(numeric-string|int $amount)
 * @method static Money EGLD(numeric-string|int $amount)
 * @method static Money EGP(numeric-string|int $amount)
 * @method static Money EIGEN(numeric-string|int $amount)
 * @method static Money ENA(numeric-string|int $amount)
 * @method static Money ENJ(numeric-string|int $amount)
 * @method static Money ENS(numeric-string|int $amount)
 * @method static Money EPIC(numeric-string|int $amount)
 * @method static Money ERN(numeric-string|int $amount)
 * @method static Money ETB(numeric-string|int $amount)
 * @method static Money ETC(numeric-string|int $amount)
 * @method static Money ETH(numeric-string|int $amount)
 * @method static Money ETHFI(numeric-string|int $amount)
 * @method static Money EUR(numeric-string|int $amount)
 * @method static Money EURI(numeric-string|int $amount)
 * @method static Money FARM(numeric-string|int $amount)
 * @method static Money FDUSD(numeric-string|int $amount)
 * @method static Money FET(numeric-string|int $amount)
 * @method static Money FIDA(numeric-string|int $amount)
 * @method static Money FIL(numeric-string|int $amount)
 * @method static Money FIO(numeric-string|int $amount)
 * @method static Money FIS(numeric-string|int $amount)
 * @method static Money FJD(numeric-string|int $amount)
 * @method static Money FKP(numeric-string|int $amount)
 * @method static Money FLM(numeric-string|int $amount)
 * @method static Money FLOKI(numeric-string|int $amount)
 * @method static Money FLOW(numeric-string|int $amount)
 * @method static Money FLUX(numeric-string|int $amount)
 * @method static Money FORM(numeric-string|int $amount)
 * @method static Money FORTH(numeric-string|int $amount)
 * @method static Money FTT(numeric-string|int $amount)
 * @method static Money FUN(numeric-string|int $amount)
 * @method static Money FXS(numeric-string|int $amount)
 * @method static Money G(numeric-string|int $amount)
 * @method static Money GALA(numeric-string|int $amount)
 * @method static Money GAS(numeric-string|int $amount)
 * @method static Money GBP(numeric-string|int $amount)
 * @method static Money GEL(numeric-string|int $amount)
 * @method static Money GHS(numeric-string|int $amount)
 * @method static Money GHST(numeric-string|int $amount)
 * @method static Money GIP(numeric-string|int $amount)
 * @method static Money GLM(numeric-string|int $amount)
 * @method static Money GLMR(numeric-string|int $amount)
 * @method static Money GMD(numeric-string|int $amount)
 * @method static Money GMT(numeric-string|int $amount)
 * @method static Money GMX(numeric-string|int $amount)
 * @method static Money GNF(numeric-string|int $amount)
 * @method static Money GNO(numeric-string|int $amount)
 * @method static Money GNS(numeric-string|int $amount)
 * @method static Money GPS(numeric-string|int $amount)
 * @method static Money GRT(numeric-string|int $amount)
 * @method static Money GTC(numeric-string|int $amount)
 * @method static Money GTQ(numeric-string|int $amount)
 * @method static Money GUN(numeric-string|int $amount)
 * @method static Money GYD(numeric-string|int $amount)
 * @method static Money HAEDAL(numeric-string|int $amount)
 * @method static Money HBAR(numeric-string|int $amount)
 * @method static Money HEI(numeric-string|int $amount)
 * @method static Money HFT(numeric-string|int $amount)
 * @method static Money HIFI(numeric-string|int $amount)
 * @method static Money HIGH(numeric-string|int $amount)
 * @method static Money HIVE(numeric-string|int $amount)
 * @method static Money HKD(numeric-string|int $amount)
 * @method static Money HMSTR(numeric-string|int $amount)
 * @method static Money HNL(numeric-string|int $amount)
 * @method static Money HOOK(numeric-string|int $amount)
 * @method static Money HOT(numeric-string|int $amount)
 * @method static Money HTG(numeric-string|int $amount)
 * @method static Money HUF(numeric-string|int $amount)
 * @method static Money HUMA(numeric-string|int $amount)
 * @method static Money HYPER(numeric-string|int $amount)
 * @method static Money ICP(numeric-string|int $amount)
 * @method static Money ICX(numeric-string|int $amount)
 * @method static Money ID(numeric-string|int $amount)
 * @method static Money IDEX(numeric-string|int $amount)
 * @method static Money IDR(numeric-string|int $amount)
 * @method static Money ILS(numeric-string|int $amount)
 * @method static Money ILV(numeric-string|int $amount)
 * @method static Money IMX(numeric-string|int $amount)
 * @method static Money INIT(numeric-string|int $amount)
 * @method static Money INJ(numeric-string|int $amount)
 * @method static Money INR(numeric-string|int $amount)
 * @method static Money IO(numeric-string|int $amount)
 * @method static Money IOST(numeric-string|int $amount)
 * @method static Money IOTA(numeric-string|int $amount)
 * @method static Money IOTX(numeric-string|int $amount)
 * @method static Money IQ(numeric-string|int $amount)
 * @method static Money IQD(numeric-string|int $amount)
 * @method static Money IRR(numeric-string|int $amount)
 * @method static Money ISK(numeric-string|int $amount)
 * @method static Money JASMY(numeric-string|int $amount)
 * @method static Money JMD(numeric-string|int $amount)
 * @method static Money JOD(numeric-string|int $amount)
 * @method static Money JOE(numeric-string|int $amount)
 * @method static Money JPY(numeric-string|int $amount)
 * @method static Money JST(numeric-string|int $amount)
 * @method static Money JTO(numeric-string|int $amount)
 * @method static Money JUP(numeric-string|int $amount)
 * @method static Money JUV(numeric-string|int $amount)
 * @method static Money KAIA(numeric-string|int $amount)
 * @method static Money KAITO(numeric-string|int $amount)
 * @method static Money KAVA(numeric-string|int $amount)
 * @method static Money KDA(numeric-string|int $amount)
 * @method static Money KERNEL(numeric-string|int $amount)
 * @method static Money KES(numeric-string|int $amount)
 * @method static Money KGS(numeric-string|int $amount)
 * @method static Money KHR(numeric-string|int $amount)
 * @method static Money KMD(numeric-string|int $amount)
 * @method static Money KMF(numeric-string|int $amount)
 * @method static Money KMNO(numeric-string|int $amount)
 * @method static Money KNC(numeric-string|int $amount)
 * @method static Money KPW(numeric-string|int $amount)
 * @method static Money KRW(numeric-string|int $amount)
 * @method static Money KSM(numeric-string|int $amount)
 * @method static Money KWD(numeric-string|int $amount)
 * @method static Money KYD(numeric-string|int $amount)
 * @method static Money KZT(numeric-string|int $amount)
 * @method static Money LAK(numeric-string|int $amount)
 * @method static Money LAYER(numeric-string|int $amount)
 * @method static Money LAZIO(numeric-string|int $amount)
 * @method static Money LBP(numeric-string|int $amount)
 * @method static Money LDO(numeric-string|int $amount)
 * @method static Money LEVER(numeric-string|int $amount)
 * @method static Money LINK(numeric-string|int $amount)
 * @method static Money LISTA(numeric-string|int $amount)
 * @method static Money LKR(numeric-string|int $amount)
 * @method static Money LOKA(numeric-string|int $amount)
 * @method static Money LPT(numeric-string|int $amount)
 * @method static Money LQTY(numeric-string|int $amount)
 * @method static Money LRC(numeric-string|int $amount)
 * @method static Money LRD(numeric-string|int $amount)
 * @method static Money LSK(numeric-string|int $amount)
 * @method static Money LSL(numeric-string|int $amount)
 * @method static Money LTC(numeric-string|int $amount)
 * @method static Money LTO(numeric-string|int $amount)
 * @method static Money LUMIA(numeric-string|int $amount)
 * @method static Money LUNA(numeric-string|int $amount)
 * @method static Money LUNC(numeric-string|int $amount)
 * @method static Money LYD(numeric-string|int $amount)
 * @method static Money MAD(numeric-string|int $amount)
 * @method static Money MAGIC(numeric-string|int $amount)
 * @method static Money MANA(numeric-string|int $amount)
 * @method static Money MANTA(numeric-string|int $amount)
 * @method static Money MASK(numeric-string|int $amount)
 * @method static Money MAV(numeric-string|int $amount)
 * @method static Money MBL(numeric-string|int $amount)
 * @method static Money MBOX(numeric-string|int $amount)
 * @method static Money MDL(numeric-string|int $amount)
 * @method static Money MDT(numeric-string|int $amount)
 * @method static Money ME(numeric-string|int $amount)
 * @method static Money MEME(numeric-string|int $amount)
 * @method static Money METIS(numeric-string|int $amount)
 * @method static Money MGA(numeric-string|int $amount)
 * @method static Money MINA(numeric-string|int $amount)
 * @method static Money MKD(numeric-string|int $amount)
 * @method static Money MKR(numeric-string|int $amount)
 * @method static Money MLN(numeric-string|int $amount)
 * @method static Money MMK(numeric-string|int $amount)
 * @method static Money MNT(numeric-string|int $amount)
 * @method static Money MOP(numeric-string|int $amount)
 * @method static Money MOVE(numeric-string|int $amount)
 * @method static Money MOVR(numeric-string|int $amount)
 * @method static Money MRU(numeric-string|int $amount)
 * @method static Money MTL(numeric-string|int $amount)
 * @method static Money MUBARAK(numeric-string|int $amount)
 * @method static Money MUR(numeric-string|int $amount)
 * @method static Money MVR(numeric-string|int $amount)
 * @method static Money MWK(numeric-string|int $amount)
 * @method static Money MXN(numeric-string|int $amount)
 * @method static Money MXV(numeric-string|int $amount)
 * @method static Money MYR(numeric-string|int $amount)
 * @method static Money MZN(numeric-string|int $amount)
 * @method static Money NAD(numeric-string|int $amount)
 * @method static Money NEAR(numeric-string|int $amount)
 * @method static Money NEIRO(numeric-string|int $amount)
 * @method static Money NEO(numeric-string|int $amount)
 * @method static Money NEXO(numeric-string|int $amount)
 * @method static Money NFP(numeric-string|int $amount)
 * @method static Money NGN(numeric-string|int $amount)
 * @method static Money NIL(numeric-string|int $amount)
 * @method static Money NIO(numeric-string|int $amount)
 * @method static Money NKN(numeric-string|int $amount)
 * @method static Money NMR(numeric-string|int $amount)
 * @method static Money NOK(numeric-string|int $amount)
 * @method static Money NOT(numeric-string|int $amount)
 * @method static Money NPR(numeric-string|int $amount)
 * @method static Money NTRN(numeric-string|int $amount)
 * @method static Money NXPC(numeric-string|int $amount)
 * @method static Money NZD(numeric-string|int $amount)
 * @method static Money OG(numeric-string|int $amount)
 * @method static Money OGN(numeric-string|int $amount)
 * @method static Money OM(numeric-string|int $amount)
 * @method static Money OMNI(numeric-string|int $amount)
 * @method static Money OMR(numeric-string|int $amount)
 * @method static Money ONDO(numeric-string|int $amount)
 * @method static Money ONE(numeric-string|int $amount)
 * @method static Money ONG(numeric-string|int $amount)
 * @method static Money ONT(numeric-string|int $amount)
 * @method static Money OP(numeric-string|int $amount)
 * @method static Money ORCA(numeric-string|int $amount)
 * @method static Money ORDI(numeric-string|int $amount)
 * @method static Money OSMO(numeric-string|int $amount)
 * @method static Money OXT(numeric-string|int $amount)
 * @method static Money PAB(numeric-string|int $amount)
 * @method static Money PARTI(numeric-string|int $amount)
 * @method static Money PAXG(numeric-string|int $amount)
 * @method static Money PEN(numeric-string|int $amount)
 * @method static Money PENDLE(numeric-string|int $amount)
 * @method static Money PENGU(numeric-string|int $amount)
 * @method static Money PEOPLE(numeric-string|int $amount)
 * @method static Money PEPE(numeric-string|int $amount)
 * @method static Money PERP(numeric-string|int $amount)
 * @method static Money PGK(numeric-string|int $amount)
 * @method static Money PHA(numeric-string|int $amount)
 * @method static Money PHB(numeric-string|int $amount)
 * @method static Money PHP(numeric-string|int $amount)
 * @method static Money PIVX(numeric-string|int $amount)
 * @method static Money PIXEL(numeric-string|int $amount)
 * @method static Money PKR(numeric-string|int $amount)
 * @method static Money PLN(numeric-string|int $amount)
 * @method static Money PNUT(numeric-string|int $amount)
 * @method static Money POL(numeric-string|int $amount)
 * @method static Money POLYX(numeric-string|int $amount)
 * @method static Money POND(numeric-string|int $amount)
 * @method static Money PORTAL(numeric-string|int $amount)
 * @method static Money PORTO(numeric-string|int $amount)
 * @method static Money POWR(numeric-string|int $amount)
 * @method static Money PROM(numeric-string|int $amount)
 * @method static Money PSG(numeric-string|int $amount)
 * @method static Money PUNDIX(numeric-string|int $amount)
 * @method static Money PYG(numeric-string|int $amount)
 * @method static Money PYR(numeric-string|int $amount)
 * @method static Money PYTH(numeric-string|int $amount)
 * @method static Money QAR(numeric-string|int $amount)
 * @method static Money QI(numeric-string|int $amount)
 * @method static Money QKC(numeric-string|int $amount)
 * @method static Money QNT(numeric-string|int $amount)
 * @method static Money QTUM(numeric-string|int $amount)
 * @method static Money QUICK(numeric-string|int $amount)
 * @method static Money RAD(numeric-string|int $amount)
 * @method static Money RARE(numeric-string|int $amount)
 * @method static Money RAY(numeric-string|int $amount)
 * @method static Money RDNT(numeric-string|int $amount)
 * @method static Money RED(numeric-string|int $amount)
 * @method static Money REI(numeric-string|int $amount)
 * @method static Money RENDER(numeric-string|int $amount)
 * @method static Money REQ(numeric-string|int $amount)
 * @method static Money REZ(numeric-string|int $amount)
 * @method static Money RIF(numeric-string|int $amount)
 * @method static Money RLC(numeric-string|int $amount)
 * @method static Money RON(numeric-string|int $amount)
 * @method static Money RONIN(numeric-string|int $amount)
 * @method static Money ROSE(numeric-string|int $amount)
 * @method static Money RPL(numeric-string|int $amount)
 * @method static Money RSD(numeric-string|int $amount)
 * @method static Money RSR(numeric-string|int $amount)
 * @method static Money RUB(numeric-string|int $amount)
 * @method static Money RUNE(numeric-string|int $amount)
 * @method static Money RVN(numeric-string|int $amount)
 * @method static Money RWF(numeric-string|int $amount)
 * @method static Money S(numeric-string|int $amount)
 * @method static Money SAGA(numeric-string|int $amount)
 * @method static Money SAND(numeric-string|int $amount)
 * @method static Money SANTOS(numeric-string|int $amount)
 * @method static Money SAR(numeric-string|int $amount)
 * @method static Money SBD(numeric-string|int $amount)
 * @method static Money SC(numeric-string|int $amount)
 * @method static Money SCR(numeric-string|int $amount)
 * @method static Money SCRT(numeric-string|int $amount)
 * @method static Money SDG(numeric-string|int $amount)
 * @method static Money SEI(numeric-string|int $amount)
 * @method static Money SEK(numeric-string|int $amount)
 * @method static Money SFP(numeric-string|int $amount)
 * @method static Money SGD(numeric-string|int $amount)
 * @method static Money SHELL(numeric-string|int $amount)
 * @method static Money SHIB(numeric-string|int $amount)
 * @method static Money SHP(numeric-string|int $amount)
 * @method static Money SIGN(numeric-string|int $amount)
 * @method static Money SKL(numeric-string|int $amount)
 * @method static Money SLE(numeric-string|int $amount)
 * @method static Money SLF(numeric-string|int $amount)
 * @method static Money SLP(numeric-string|int $amount)
 * @method static Money SNX(numeric-string|int $amount)
 * @method static Money SOL(numeric-string|int $amount)
 * @method static Money SOLV(numeric-string|int $amount)
 * @method static Money SOPH(numeric-string|int $amount)
 * @method static Money SOS(numeric-string|int $amount)
 * @method static Money SPELL(numeric-string|int $amount)
 * @method static Money SRD(numeric-string|int $amount)
 * @method static Money SSP(numeric-string|int $amount)
 * @method static Money SSV(numeric-string|int $amount)
 * @method static Money STEEM(numeric-string|int $amount)
 * @method static Money STG(numeric-string|int $amount)
 * @method static Money STN(numeric-string|int $amount)
 * @method static Money STO(numeric-string|int $amount)
 * @method static Money STORJ(numeric-string|int $amount)
 * @method static Money STRAX(numeric-string|int $amount)
 * @method static Money STRK(numeric-string|int $amount)
 * @method static Money STX(numeric-string|int $amount)
 * @method static Money SUI(numeric-string|int $amount)
 * @method static Money SUN(numeric-string|int $amount)
 * @method static Money SUPER(numeric-string|int $amount)
 * @method static Money SUSHI(numeric-string|int $amount)
 * @method static Money SVC(numeric-string|int $amount)
 * @method static Money SXP(numeric-string|int $amount)
 * @method static Money SXT(numeric-string|int $amount)
 * @method static Money SYN(numeric-string|int $amount)
 * @method static Money SYP(numeric-string|int $amount)
 * @method static Money SYRUP(numeric-string|int $amount)
 * @method static Money SYS(numeric-string|int $amount)
 * @method static Money SZL(numeric-string|int $amount)
 * @method static Money T(numeric-string|int $amount)
 * @method static Money TAO(numeric-string|int $amount)
 * @method static Money TFUEL(numeric-string|int $amount)
 * @method static Money THB(numeric-string|int $amount)
 * @method static Money THE(numeric-string|int $amount)
 * @method static Money THETA(numeric-string|int $amount)
 * @method static Money TIA(numeric-string|int $amount)
 * @method static Money TJS(numeric-string|int $amount)
 * @method static Money TKO(numeric-string|int $amount)
 * @method static Money TLM(numeric-string|int $amount)
 * @method static Money TMT(numeric-string|int $amount)
 * @method static Money TND(numeric-string|int $amount)
 * @method static Money TNSR(numeric-string|int $amount)
 * @method static Money TON(numeric-string|int $amount)
 * @method static Money TOP(numeric-string|int $amount)
 * @method static Money TRB(numeric-string|int $amount)
 * @method static Money TRU(numeric-string|int $amount)
 * @method static Money TRUMP(numeric-string|int $amount)
 * @method static Money TRX(numeric-string|int $amount)
 * @method static Money TRY(numeric-string|int $amount)
 * @method static Money TST(numeric-string|int $amount)
 * @method static Money TTD(numeric-string|int $amount)
 * @method static Money TURBO(numeric-string|int $amount)
 * @method static Money TUSD(numeric-string|int $amount)
 * @method static Money TUT(numeric-string|int $amount)
 * @method static Money TWD(numeric-string|int $amount)
 * @method static Money TWT(numeric-string|int $amount)
 * @method static Money TZS(numeric-string|int $amount)
 * @method static Money UAH(numeric-string|int $amount)
 * @method static Money UGX(numeric-string|int $amount)
 * @method static Money UMA(numeric-string|int $amount)
 * @method static Money UNI(numeric-string|int $amount)
 * @method static Money USD(numeric-string|int $amount)
 * @method static Money USD1(numeric-string|int $amount)
 * @method static Money USDC(numeric-string|int $amount)
 * @method static Money USDP(numeric-string|int $amount)
 * @method static Money USDT(numeric-string|int $amount)
 * @method static Money USN(numeric-string|int $amount)
 * @method static Money USTC(numeric-string|int $amount)
 * @method static Money USUAL(numeric-string|int $amount)
 * @method static Money UTK(numeric-string|int $amount)
 * @method static Money UYI(numeric-string|int $amount)
 * @method static Money UYU(numeric-string|int $amount)
 * @method static Money UYW(numeric-string|int $amount)
 * @method static Money UZS(numeric-string|int $amount)
 * @method static Money VANA(numeric-string|int $amount)
 * @method static Money VANRY(numeric-string|int $amount)
 * @method static Money VED(numeric-string|int $amount)
 * @method static Money VELODROME(numeric-string|int $amount)
 * @method static Money VES(numeric-string|int $amount)
 * @method static Money VET(numeric-string|int $amount)
 * @method static Money VIC(numeric-string|int $amount)
 * @method static Money VIRTUAL(numeric-string|int $amount)
 * @method static Money VND(numeric-string|int $amount)
 * @method static Money VOXEL(numeric-string|int $amount)
 * @method static Money VTHO(numeric-string|int $amount)
 * @method static Money VUV(numeric-string|int $amount)
 * @method static Money W(numeric-string|int $amount)
 * @method static Money WAN(numeric-string|int $amount)
 * @method static Money WAXP(numeric-string|int $amount)
 * @method static Money WBETH(numeric-string|int $amount)
 * @method static Money WBTC(numeric-string|int $amount)
 * @method static Money WCT(numeric-string|int $amount)
 * @method static Money WIF(numeric-string|int $amount)
 * @method static Money WIN(numeric-string|int $amount)
 * @method static Money WLD(numeric-string|int $amount)
 * @method static Money WOO(numeric-string|int $amount)
 * @method static Money WST(numeric-string|int $amount)
 * @method static Money XAD(numeric-string|int $amount)
 * @method static Money XAF(numeric-string|int $amount)
 * @method static Money XAG(numeric-string|int $amount)
 * @method static Money XAI(numeric-string|int $amount)
 * @method static Money XAU(numeric-string|int $amount)
 * @method static Money XBA(numeric-string|int $amount)
 * @method static Money XBB(numeric-string|int $amount)
 * @method static Money XBC(numeric-string|int $amount)
 * @method static Money XBD(numeric-string|int $amount)
 * @method static Money XBT(numeric-string|int $amount)
 * @method static Money XCD(numeric-string|int $amount)
 * @method static Money XCG(numeric-string|int $amount)
 * @method static Money XDR(numeric-string|int $amount)
 * @method static Money XEC(numeric-string|int $amount)
 * @method static Money XLM(numeric-string|int $amount)
 * @method static Money XNO(numeric-string|int $amount)
 * @method static Money XOF(numeric-string|int $amount)
 * @method static Money XPD(numeric-string|int $amount)
 * @method static Money XPF(numeric-string|int $amount)
 * @method static Money XPT(numeric-string|int $amount)
 * @method static Money XRP(numeric-string|int $amount)
 * @method static Money XSU(numeric-string|int $amount)
 * @method static Money XTS(numeric-string|int $amount)
 * @method static Money XTZ(numeric-string|int $amount)
 * @method static Money XUA(numeric-string|int $amount)
 * @method static Money XUSD(numeric-string|int $amount)
 * @method static Money XVG(numeric-string|int $amount)
 * @method static Money XVS(numeric-string|int $amount)
 * @method static Money XXX(numeric-string|int $amount)
 * @method static Money YER(numeric-string|int $amount)
 * @method static Money YFI(numeric-string|int $amount)
 * @method static Money YGG(numeric-string|int $amount)
 * @method static Money ZAR(numeric-string|int $amount)
 * @method static Money ZEC(numeric-string|int $amount)
 * @method static Money ZEN(numeric-string|int $amount)
 * @method static Money ZIL(numeric-string|int $amount)
 * @method static Money ZK(numeric-string|int $amount)
 * @method static Money ZMW(numeric-string|int $amount)
 * @method static Money ZRO(numeric-string|int $amount)
 * @method static Money ZRX(numeric-string|int $amount)
 * @method static Money ZWG(numeric-string|int $amount)
 * @psalm-immutable
 */
trait MoneyFactory
{
    /**
     * Convenience factory method for a Money object.
     *
     * <code>
     * $fiveDollar = Money::USD(500);
     * </code>
     *
     * @param non-empty-string          $method
     * @param array{numeric-string|int} $arguments
     *
     * @throws InvalidArgumentException If amount is not integer(ish).
     *
     * @psalm-pure
     */
    public static function __callStatic(string $method, array $arguments): Money
    {
        return new Money($arguments[0], new Currency($method));
    }
}
